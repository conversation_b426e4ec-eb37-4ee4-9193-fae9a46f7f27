"""KiotViet API client implementation."""

import asyncio
from typing import Optional, Union, Dict, Any, List
from datetime import datetime
import httpx
from loguru import logger
from ..config.config import Settings as KiotVietConfig
from ..models.models import CategoryResponse, InvoiceResponse, ProductOnHandResponse, BranchResponse, Product, CustomerResponse, PurchaseOrderResponse, ReturnResponse, CashFlowResponse
from .auth import TokenManager


class KiotVietAPIClient:
    """Client for interacting with KiotViet Public API.

    This class provides all functionality for KiotViet API integration including
    authentication, retry logic, error handling, and specific API methods.
    """

    def __init__(self, config: KiotVietConfig):
        """Initialize the KiotViet API client.

        Args:
            config: KiotViet configuration
        """
        self.config = config
        self.token_manager = TokenManager(config)
        self._client: Optional[httpx.AsyncClient] = None

    async def __aenter__(self) -> "KiotVietAPIClient":
        """Initialize HTTP client and authenticate."""
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.config.kiotviet_request_timeout),
            limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
        )
        return self

    async def __aexit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """Clean up HTTP client."""
        if self._client:
            await self._client.aclose()
            self._client = None

    async def _get_headers(self) -> Dict[str, str]:
        """Get headers with authentication token."""
        access_token = await self.token_manager.get_access_token()
        return {
            "Retailer": self.config.kiotviet_retailer,
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

    async def make_request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make authenticated request to KiotViet API with retry logic.

        This method handles:
        - Authentication with automatic token refresh
        - Retry logic with exponential backoff
        - Error handling and logging
        - Request/response formatting

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint path or full URL
            data: Request body data (for POST/PUT requests)

        Returns:
            Parsed JSON response from the API

        Raises:
            RuntimeError: If client is not initialized
            httpx.HTTPStatusError: For HTTP error responses
            httpx.RequestError: For network/connection errors
        """
        if not self._client:
            raise RuntimeError("Client not initialized. Use async context manager.")

        headers = await self._get_headers()
        url = endpoint if endpoint.startswith('http') else f"{self.config.kiotviet_api_base_url}{endpoint}"

        for attempt in range(self.config.kiotviet_max_retries):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1})")

                response = await self._client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data if method in ['POST', 'PUT'] else None,
                    params=data if method == 'GET' else None
                )

                response.raise_for_status()

                try:
                    result = response.json()
                    logger.debug(f"Successful {method} request to {url}")
                    return result
                except ValueError as e:
                    logger.error(f"Invalid JSON response from {url}: {e}")
                    raise

            except httpx.HTTPStatusError as e:
                logger.warning(f"HTTP error for {method} {url}: {e.response.status_code} {e.response.text}")

                # Handle authentication errors by refreshing token
                if e.response.status_code == 401:
                    logger.info("Authentication failed, refreshing token...")
                    # Token manager will handle refresh on next call
                    headers = await self._get_headers()
                    continue

                # Don't retry client errors (4xx) except 401
                if 400 <= e.response.status_code < 500 and e.response.status_code != 401:
                    raise

                # Retry server errors (5xx) and other retryable errors
                if attempt == self.config.kiotviet_max_retries - 1:
                    raise

                # Exponential backoff
                wait_time = (2 ** attempt) * self.config.kiotviet_retry_delay
                logger.info(f"Retrying in {wait_time}s...")
                await asyncio.sleep(wait_time)

            except httpx.RequestError as e:
                logger.warning(f"Request error for {method} {url}: {e}")

                if attempt == self.config.kiotviet_max_retries - 1:
                    raise

                # Exponential backoff
                wait_time = (2 ** attempt) * self.config.kiotviet_retry_delay
                logger.info(f"Retrying in {wait_time}s...")
                await asyncio.sleep(wait_time)

    async def get_categories(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> CategoryResponse:
        """Get product categories from KiotViet API.

        Args:
            page_size: Number of items per page (max 100)
            current_item: Starting item index for pagination
            order_direction: Sort order ("Asc" or "Desc")
            hierarchical_data: Whether to return hierarchical structure

        Returns:
            Validated CategoryResponse with typed data
        """
        data = {
            "pageSize": min(page_size, 100),  # Ensure max limit
            "currentItem": current_item,
            "orderDirection": order_direction,
            "hierarchicalData": hierarchical_data
        }

        raw_response = await self.make_request("GET", "/categories", data=data)
        return CategoryResponse.model_validate(raw_response)

    async def get_invoices(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        branch_ids: Optional[List[int]] = None,
        customer_ids: Optional[List[int]] = None,
        customer_code: Optional[str] = None,
        status: Optional[List[int]] = None,
        include_payment: Optional[bool] = None,
        include_invoice_delivery: Optional[bool] = None,
        last_modified_from: Optional[Union[str, datetime]] = None,
        to_date: Optional[Union[str, datetime]] = None,
        created_date: Optional[Union[str, datetime]] = None,
        from_purchase_date: Optional[Union[str, datetime]] = None,
        to_purchase_date: Optional[Union[str, datetime]] = None,
        order_id: Optional[int] = None,
        order_by: Optional[str] = None
    ) -> InvoiceResponse:
        """Lấy danh sách hóa đơn từ KiotViet API với các tùy chọn lọc theo API chính thức.

        Args:
            page_size: Số lượng mục trên mỗi trang (tối đa 100, mặc định 20)
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Thứ tự sắp xếp ("Asc" hoặc "Desc")
            branch_ids: Danh sách ID chi nhánh
            customer_ids: Danh sách ID khách hàng
            customer_code: Mã khách hàng
            status: Danh sách tình trạng hóa đơn
            include_payment: Có lấy thông tin thanh toán
            include_invoice_delivery: Có lấy thông tin giao hàng
            last_modified_from: Thời gian cập nhật từ
            to_date: Thời gian cập nhật đến
            created_date: Thời gian tạo
            from_purchase_date: Từ ngày giao dịch
            to_purchase_date: Đến ngày giao dịch
            order_id: ID của đơn đặt hàng
            order_by: Trường để sắp xếp theo

        Returns:
            InvoiceResponse đã được xác thực với dữ liệu hóa đơn có kiểu

        Raises:
            ValueError: Nếu tham số không hợp lệ
            httpx.HTTPStatusError: Cho phản hồi lỗi HTTP
            httpx.RequestError: Cho lỗi mạng/kết nối
        """

        data = {
            "pageSize": min(page_size, 100),
            "currentItem": current_item,
            "orderDirection": order_direction
        }

        # Thêm các tham số theo API chính thức
        if branch_ids:
            data["branchIds"] = branch_ids
        if customer_ids:
            data["customerIds"] = customer_ids
        if customer_code:
            data["customerCode"] = customer_code
        if status:
            data["status"] = status
        if include_payment is not None:
            data["includePayment"] = include_payment
        if include_invoice_delivery is not None:
            data["includeInvoiceDelivery"] = include_invoice_delivery
        if last_modified_from:
            data["lastModifiedFrom"] = last_modified_from
        if to_date:
            data["toDate"] = to_date
        if created_date:
            data["createdDate"] = created_date
        if from_purchase_date:
            data["fromPurchaseDate"] = from_purchase_date
        if to_purchase_date:
            data["toPurchaseDate"] = to_purchase_date
        if order_id is not None:
            data["orderId"] = order_id
        if order_by:
            data["orderBy"] = order_by

        raw_response = await self.make_request("GET", "/invoices", data=data)
        return InvoiceResponse.model_validate(raw_response)

    async def get_product_on_hands(
        self,
        page_size: int = 20,
        current_item: int = 0,
        order_by: Optional[str] = None,
        last_modified_from: Optional[Union[str, datetime]] = None,
        branch_ids: Optional[list[int]] = None
    ) -> ProductOnHandResponse:
        """Get product inventory/stock data from KiotViet API.

        Args:
            page_size: Number of items per page (max 100, default 20)
            current_item: Starting item index for pagination
            order_by: Sort data by field (e.g., "Code")
            last_modified_from: Filter by last modified time (datetime object or ISO string)
            branch_ids: List of branch IDs to filter by

        Returns:
            Validated ProductOnHandResponse with inventory data

        Raises:
            ValueError: If parameters are invalid
            httpx.HTTPStatusError: For HTTP error responses
            httpx.RequestError: For network/connection errors
        """
        data = {
            "pageSize": min(page_size, 100),  # Ensure max limit
            "currentItem": current_item
        }

        # Add optional parameters if provided
        if order_by:
            data["orderBy"] = order_by
        if last_modified_from:
            data["lastModifiedFrom"] = last_modified_from
        if branch_ids:
            data["branchIds"] = branch_ids

        raw_response = await self.make_request("GET", "/productOnHands", data=data)
        return ProductOnHandResponse.model_validate(raw_response)

    async def get_branches(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc"
    ) -> BranchResponse:
        """Get branches from KiotViet API.

        Args:
            page_size: Number of items per page (max 100, default 50)
            current_item: Starting item index for pagination
            order_direction: Sort order ("Asc" or "Desc")

        Returns:
            Validated BranchResponse with branch data

        Raises:
            ValueError: If parameters are invalid
            httpx.HTTPStatusError: For HTTP error responses
            httpx.RequestError: For network/connection errors
        """
        data = {
            "pageSize": min(page_size, 100),  # Ensure max limit
            "currentItem": current_item,
            "orderDirection": order_direction
        }

        raw_response = await self.make_request("GET", "/branches", data=data)
        return BranchResponse.model_validate(raw_response)

    async def get_product_by_code(self, code: str) -> Product:
        """Get product details by product code from KiotViet API.

        Args:
            code: Product code to search for

        Returns:
            Validated Product with detailed product information

        Raises:
            ValueError: If product code is empty or invalid
            httpx.HTTPStatusError: For HTTP error responses (404 if product not found)
            httpx.RequestError: For network/connection errors
        """
        if not code or not code.strip():
            raise ValueError("Product code cannot be empty")

        # URL encode the code to handle special characters
        from urllib.parse import quote
        encoded_code = quote(code.strip())
        
        raw_response = await self.make_request("GET", f"/products/code/{encoded_code}")
        return Product.model_validate(raw_response)

    async def get_customers(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        code: Optional[str] = None,
        name: Optional[str] = None,
        contact_number: Optional[str] = None,
        last_modified_from: Optional[Union[str, datetime]] = None,
        order_by: Optional[str] = None,
        include_remove_ids: Optional[bool] = None,
        include_total: Optional[bool] = None,
        include_customer_group: Optional[bool] = None,
        include_customer_social: Optional[bool] = None,
        birth_date: Optional[str] = None,
        group_id: Optional[int] = None
    ) -> CustomerResponse:
        """Lấy danh sách khách hàng từ KiotViet API với các tùy chọn lọc theo API chính thức.

        Args:
            page_size: Số lượng mục trên mỗi trang (tối đa 100, mặc định 20)
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Thứ tự sắp xếp ("Asc" hoặc "Desc")
            code: Tìm kiếm khách hàng theo mã khách hàng
            name: Tìm kiếm theo tên khách hàng
            contact_number: Tìm kiếm theo số điện thoại khách hàng
            last_modified_from: Thời gian cập nhật
            order_by: Sắp xếp dữ liệu theo trường
            include_remove_ids: Có lấy thông tin danh sách ID bị xóa
            include_total: Có lấy thông tin TotalInvoice, TotalPoint, TotalRevenue
            include_customer_group: Có lấy thông tin nhóm khách hàng
            include_customer_social: Có lấy thông tin Psid facebook fanpage
            birth_date: Lọc khách hàng theo ngày sinh nhật
            group_id: Lọc theo nhóm khách hàng

        Returns:
            CustomerResponse đã được xác thực với dữ liệu khách hàng có kiểu

        Raises:
            ValueError: Nếu tham số không hợp lệ
            httpx.HTTPStatusError: Cho phản hồi lỗi HTTP
            httpx.RequestError: Cho lỗi mạng/kết nối
        """
        data = {
            "pageSize": min(page_size, 100),  # Đảm bảo giới hạn tối đa
            "currentItem": current_item,
            "orderDirection": order_direction
        }

        # Thêm các tham số theo API chính thức
        if code:
            data["code"] = code
        if name:
            data["name"] = name
        if contact_number:
            data["contactNumber"] = contact_number
        if last_modified_from:
            data["lastModifiedFrom"] = last_modified_from
        if order_by:
            data["orderBy"] = order_by
        if include_remove_ids is not None:
            data["includeRemoveIds"] = include_remove_ids
        if include_total is not None:
            data["includeTotal"] = include_total
        if include_customer_group is not None:
            data["includeCustomerGroup"] = include_customer_group
        if include_customer_social is not None:
            data["includeCustomerSocial"] = include_customer_social
        if birth_date:
            data["birthDate"] = birth_date
        if group_id is not None:
            data["groupId"] = group_id

        raw_response = await self.make_request("GET", "/customers", data=data)
        return CustomerResponse.model_validate(raw_response)

    async def get_purchase_orders(
        self,
        page_size: int = 20,
        current_item: int = 0,
        order_direction: str = "Asc",
        # Tham số chuẩn theo API spec
        branchIds: Optional[List[int]] = None,
        status: Optional[List[int]] = None,
        includePayment: Optional[bool] = None,
        includeOrderDelivery: Optional[bool] = None,
        fromPurchaseDate: Optional[Union[str, datetime]] = None,
        toPurchaseDate: Optional[Union[str, datetime]] = None,
        # Tham số bổ sung (backward compatibility)
        from_purchase_date: Optional[Union[str, datetime]] = None,
        to_purchase_date: Optional[Union[str, datetime]] = None,
        from_created_date: Optional[Union[str, datetime]] = None,
        to_created_date: Optional[Union[str, datetime]] = None,
        from_modified_date: Optional[Union[str, datetime]] = None,
        to_modified_date: Optional[Union[str, datetime]] = None,
        ids: Optional[List[int]] = None,
        branch_ids: Optional[List[int]] = None,
        supplier_ids: Optional[List[int]] = None,
        order_by: Optional[str] = None,
        include_purchase_order_details: Optional[bool] = None
    ) -> PurchaseOrderResponse:
        """Lấy danh sách đơn nhập hàng từ KiotViet API theo spec chuẩn.

        Args:
            page_size: Số lượng mục trên mỗi trang (mặc định 20, tối đa 100)
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Thứ tự sắp xếp ("Asc" hoặc "Desc")

            # Tham số chuẩn theo API spec
            branchIds: ID chi nhánh (danh sách)
            status: Tình trạng đặt hàng (danh sách)
            includePayment: Có lấy thông tin thanh toán
            includeOrderDelivery: Có lấy thông tin giao hàng
            fromPurchaseDate: Từ ngày nhập hàng
            toPurchaseDate: Đến ngày nhập hàng

            # Tham số bổ sung (backward compatibility)
            from_purchase_date: Ngày bắt đầu cho bộ lọc ngày nhập hàng (deprecated)
            to_purchase_date: Ngày kết thúc cho bộ lọc ngày nhập hàng (deprecated)
            from_created_date: Ngày bắt đầu cho bộ lọc ngày tạo
            to_created_date: Ngày kết thúc cho bộ lọc ngày tạo
            from_modified_date: Ngày bắt đầu cho bộ lọc ngày sửa đổi
            to_modified_date: Ngày kết thúc cho bộ lọc ngày sửa đổi
            ids: Danh sách ID đơn nhập hàng cụ thể cần lấy
            branch_ids: Danh sách ID chi nhánh để lọc theo (deprecated)
            supplier_ids: Danh sách ID nhà cung cấp để lọc theo
            order_by: Trường để sắp xếp theo
            include_purchase_order_details: Bao gồm chi tiết đơn nhập hàng

        Returns:
            PurchaseOrderResponse đã được xác thực với dữ liệu đơn nhập hàng có kiểu

        Raises:
            ValueError: Nếu tham số không hợp lệ
            httpx.HTTPStatusError: Cho phản hồi lỗi HTTP
            httpx.RequestError: Cho lỗi mạng/kết nối
        """
        data = {
            "pageSize": min(page_size, 100),  # Đảm bảo giới hạn tối đa
            "currentItem": current_item,
            "orderDirection": order_direction
        }

        # Tham số chuẩn theo API spec (ưu tiên)
        if branchIds:
            data["branchIds"] = branchIds
        elif branch_ids:  # Backward compatibility
            data["branchIds"] = branch_ids

        if status is not None:
            data["status"] = status

        if includePayment is not None:
            data["includePayment"] = includePayment

        if includeOrderDelivery is not None:
            data["includeOrderDelivery"] = includeOrderDelivery

        if fromPurchaseDate:
            data["fromPurchaseDate"] = fromPurchaseDate
        elif from_purchase_date:  # Backward compatibility
            data["fromPurchaseDate"] = from_purchase_date

        if toPurchaseDate:
            data["toPurchaseDate"] = toPurchaseDate
        elif to_purchase_date:  # Backward compatibility
            data["toPurchaseDate"] = to_purchase_date

        # Các tham số bổ sung
        if from_created_date:
            data["fromCreatedDate"] = from_created_date
        if to_created_date:
            data["toCreatedDate"] = to_created_date
        if from_modified_date:
            data["fromModifiedDate"] = from_modified_date
        if to_modified_date:
            data["toModifiedDate"] = to_modified_date
        if ids:
            data["ids"] = ids
        if supplier_ids:
            data["supplierIds"] = supplier_ids
        if order_by:
            data["orderBy"] = order_by
        if include_purchase_order_details is not None:
            data["includePurchaseOrderDetails"] = include_purchase_order_details

        raw_response = await self.make_request("GET", "/purchaseorders", data=data)
        return PurchaseOrderResponse.model_validate(raw_response)

    async def get_returns(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        from_return_date: Optional[Union[str, datetime]] = None,
        to_return_date: Optional[Union[str, datetime]] = None,
        from_created_date: Optional[Union[str, datetime]] = None,
        to_created_date: Optional[Union[str, datetime]] = None,
        from_modified_date: Optional[Union[str, datetime]] = None,
        to_modified_date: Optional[Union[str, datetime]] = None,
        ids: Optional[List[int]] = None,
        branch_ids: Optional[List[int]] = None,
        customer_ids: Optional[List[int]] = None,
        invoice_ids: Optional[List[int]] = None,
        status: Optional[int] = None,
        order_by: Optional[str] = None,
        include_return_details: Optional[bool] = None
    ) -> ReturnResponse:
        """Lấy danh sách đơn trả hàng từ KiotViet API với các tùy chọn lọc toàn diện.

        Args:
            page_size: Số lượng mục trên mỗi trang (tối đa 100)
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Thứ tự sắp xếp ("Asc" hoặc "Desc")
            from_return_date: Ngày bắt đầu cho bộ lọc ngày trả hàng
            to_return_date: Ngày kết thúc cho bộ lọc ngày trả hàng
            from_created_date: Ngày bắt đầu cho bộ lọc ngày tạo
            to_created_date: Ngày kết thúc cho bộ lọc ngày tạo
            from_modified_date: Ngày bắt đầu cho bộ lọc ngày sửa đổi
            to_modified_date: Ngày kết thúc cho bộ lọc ngày sửa đổi
            ids: Danh sách ID đơn trả hàng cụ thể cần lấy
            branch_ids: Danh sách ID chi nhánh để lọc theo
            customer_ids: Danh sách ID khách hàng để lọc theo
            invoice_ids: Danh sách ID hóa đơn gốc để lọc theo
            status: Lọc theo trạng thái đơn trả hàng
            order_by: Trường để sắp xếp theo
            include_return_details: Bao gồm chi tiết đơn trả hàng

        Returns:
            ReturnResponse đã được xác thực với dữ liệu đơn trả hàng có kiểu

        Raises:
            ValueError: Nếu tham số không hợp lệ
            httpx.HTTPStatusError: Cho phản hồi lỗi HTTP
            httpx.RequestError: Cho lỗi mạng/kết nối
        """
        data = {
            "pageSize": min(page_size, 100),  # Đảm bảo giới hạn tối đa
            "currentItem": current_item,
            "orderDirection": order_direction
        }

        # Thêm các bộ lọc ngày tùy chọn
        if from_return_date:
            data["fromReturnDate"] = from_return_date
        if to_return_date:
            data["toReturnDate"] = to_return_date
        if from_created_date:
            data["fromCreatedDate"] = from_created_date
        if to_created_date:
            data["toCreatedDate"] = to_created_date
        if from_modified_date:
            data["fromModifiedDate"] = from_modified_date
        if to_modified_date:
            data["toModifiedDate"] = to_modified_date

        # Thêm các bộ lọc tùy chọn
        if ids:
            data["ids"] = ids
        if branch_ids:
            data["branchIds"] = branch_ids
        if customer_ids:
            data["customerIds"] = customer_ids
        if invoice_ids:
            data["invoiceIds"] = invoice_ids
        if status is not None:
            data["status"] = status
        if order_by:
            data["orderBy"] = order_by
        if include_return_details is not None:
            data["includeReturnDetails"] = include_return_details

        raw_response = await self.make_request("GET", "/returns", data=data)
        return ReturnResponse.model_validate(raw_response)

    async def get_cashflow(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        branch_ids: Optional[List[int]] = None,
        codes: Optional[List[str]] = None,
        user_id: Optional[int] = None,
        account_id: Optional[int] = None,
        partner_type: Optional[str] = None,
        methods: Optional[List[str]] = None,
        cashflow_group_ids: Optional[List[int]] = None,
        used_for_financial_reporting: Optional[int] = None,
        partner_name: Optional[str] = None,
        contact_number: Optional[str] = None,
        is_receipt: Optional[bool] = None,
        include_account: Optional[bool] = None,
        include_branch: Optional[bool] = None,
        include_user: Optional[bool] = None,
        start_date: Optional[Union[str, datetime]] = None,
        end_date: Optional[Union[str, datetime]] = None,
        status: Optional[int] = None,
        ids: Optional[List[int]] = None,
        order_by: Optional[str] = None
    ) -> CashFlowResponse:
        """Lấy danh sách phiếu thu chi từ KiotViet API với các tùy chọn lọc toàn diện theo API chính thức.

        Args:
            page_size: Số lượng mục trên mỗi trang (tối đa 100, mặc định 20)
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Thứ tự sắp xếp ("Asc" hoặc "Desc")
            branch_ids: Danh sách ID chi nhánh
            codes: Danh sách mã code của phiếu
            user_id: ID người tạo
            account_id: ID tài khoản nhận
            partner_type: Loại người nộp/nhận (A/C/S/U/D/O)
            methods: Danh sách phương thức thanh toán
            cashflow_group_ids: Danh sách ID loại thu/chi
            used_for_financial_reporting: Lọc theo kết quả kinh doanh (0/1)
            partner_name: Tên người nộp/nhận
            contact_number: Số điện thoại người nộp/nhận
            is_receipt: Phiếu thu/chi (True=thu, False=chi)
            include_account: Lấy thông tin tài khoản ngân hàng
            include_branch: Lấy tên chi nhánh
            include_user: Lấy tên người tạo
            start_date: Thời gian bắt đầu
            end_date: Thời gian kết thúc
            status: Trạng thái phiếu (0=Đã thanh toán, 1=Đã hủy)
            ids: Danh sách ID phiếu thu chi cụ thể
            order_by: Trường để sắp xếp theo

        Returns:
            CashFlowResponse đã được xác thực với dữ liệu phiếu thu chi có kiểu

        Raises:
            ValueError: Nếu tham số không hợp lệ
            httpx.HTTPStatusError: Cho phản hồi lỗi HTTP
            httpx.RequestError: Cho lỗi mạng/kết nối
        """
        data = {
            "pageSize": min(page_size, 100),  # Đảm bảo giới hạn tối đa
            "currentItem": current_item,
            "orderDirection": order_direction
        }

        # Thêm các tham số theo API chính thức
        if branch_ids:
            data["branchIds"] = branch_ids
        if codes:
            data["code"] = codes
        if user_id is not None:
            data["userId"] = user_id
        if account_id is not None:
            data["accountId"] = account_id
        if partner_type:
            data["partnerType"] = partner_type
        if methods:
            data["method"] = methods
        if cashflow_group_ids:
            data["cashFlowGroupId"] = cashflow_group_ids
        if used_for_financial_reporting is not None:
            data["usedForFinancialReporting"] = used_for_financial_reporting
        if partner_name:
            data["partnerName"] = partner_name
        if contact_number:
            data["contactNumber"] = contact_number
        if is_receipt is not None:
            data["isReceipt"] = is_receipt
        if include_account is not None:
            data["includeAccount"] = include_account
        if include_branch is not None:
            data["includeBranch"] = include_branch
        if include_user is not None:
            data["includeUser"] = include_user
        if start_date:
            data["startDate"] = start_date
        if end_date:
            data["endDate"] = end_date
        if status is not None:
            data["status"] = status
        if ids:
            data["ids"] = ids
        if order_by:
            data["orderBy"] = order_by

        raw_response = await self.make_request("GET", "/cashflow", data=data)
        return CashFlowResponse.model_validate(raw_response)
