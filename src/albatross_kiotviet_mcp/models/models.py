"""Data models for KiotViet API responses."""

from typing import List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field


class Category(BaseModel):
    """Individual category data model."""
    id: int = Field(alias="categoryId")  # KiotViet API uses 'categoryId' instead of 'id'
    categoryName: str
    categoryCode: Optional[str] = None
    parentId: Optional[int] = None
    hasChild: bool = False
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None
    rank: Optional[int] = None  # Add rank field that appears in API response


class CategoryResponse(BaseModel):
    """Response model for categories API endpoint."""
    total: int
    pageSize: int
    data: List[Category]


class InvoicePayment(BaseModel):
    """Mô hình dữ liệu thanh toán hóa đơn."""
    id: int
    code: str
    amount: float
    method: str
    status: Optional[int] = None
    statusValue: Optional[str] = None
    transDate: Optional[datetime] = None
    bankAccount: Optional[str] = None
    accountId: Optional[int] = None


class InvoiceOrderSurcharge(BaseModel):
    """Mô hình dữ liệu phụ phí hóa đơn."""
    id: int
    invoiceId: Optional[int] = None
    surchargeId: Optional[int] = None
    surchargeName: str
    surValue: Optional[float] = None
    price: Optional[float] = None
    createdDate: Optional[datetime] = None


class ProductBatchExpire(BaseModel):
    """Mô hình dữ liệu lô hàng có hạn sử dụng."""
    id: int
    productId: int
    batchName: str
    fullNameVirgule: str
    createdDate: Optional[datetime] = None
    expireDate: Optional[datetime] = None


class InvoiceDetail(BaseModel):
    """Mô hình dữ liệu chi tiết hóa đơn."""
    productId: int
    productCode: str
    productName: str
    quantity: float
    price: float
    discountRatio: Optional[float] = None
    discount: Optional[float] = None
    note: Optional[str] = None
    serialNumbers: Optional[str] = None
    productBatchExpire: Optional[ProductBatchExpire] = None


class SaleChannel(BaseModel):
    """Mô hình dữ liệu kênh bán hàng."""
    IsNotDelete: bool
    RetailerId: int
    Position: int
    IsActivate: bool
    CreatedBy: int
    CreatedDate: Optional[datetime] = None
    Id: int
    Name: str


class PartnerDelivery(BaseModel):
    """Mô hình dữ liệu đối tác giao hàng."""
    code: str
    name: str
    address: str
    contactNumber: str
    email: str


class InvoiceDelivery(BaseModel):
    """Mô hình dữ liệu thông tin giao hàng."""
    deliveryCode: str
    type: Optional[int] = None
    status: int
    statusValue: str
    price: Optional[float] = None
    receiver: str
    contactNumber: str
    address: str
    locationId: Optional[int] = None
    locationName: Optional[str] = None
    usingPriceCod: bool
    priceCodPayment: float
    weight: Optional[float] = None
    length: Optional[float] = None
    width: Optional[float] = None
    height: Optional[float] = None
    partnerDeliveryId: Optional[int] = None
    partnerDelivery: Optional[PartnerDelivery] = None


class Invoice(BaseModel):
    """Mô hình dữ liệu hóa đơn riêng lẻ với đầy đủ thông tin theo API chính thức."""
    id: int
    code: str
    purchaseDate: Optional[datetime] = None
    branchId: Optional[int] = None
    branchName: Optional[str] = None
    soldById: Optional[int] = None
    soldByName: Optional[str] = None
    customerId: Optional[int] = None
    customerCode: Optional[str] = None
    customerName: Optional[str] = None
    total: Optional[float] = None
    totalPayment: Optional[float] = None
    status: Optional[int] = None
    statusValue: Optional[str] = None
    usingCod: Optional[bool] = None
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None
    
    # Thông tin bổ sung (chỉ có khi includePayment=True)
    payments: Optional[List[InvoicePayment]] = None
    
    # Thông tin phụ phí
    invoiceOrderSurcharges: Optional[List[InvoiceOrderSurcharge]] = None
    
    # Chi tiết hóa đơn - danh sách các sản phẩm trong hóa đơn
    invoiceDetails: Optional[List[InvoiceDetail]] = None
    
    # Kênh bán hàng
    SaleChannel: Optional[SaleChannel] = None
    
    # Thông tin giao hàng (chỉ có khi includeInvoiceDelivery=True)
    invoiceDelivery: Optional[InvoiceDelivery] = None


class InvoiceResponse(BaseModel):
    """Mô hình phản hồi cho endpoint API hóa đơn."""
    total: int
    pageSize: int
    data: List[Invoice]


class Inventory(BaseModel):
    """Individual inventory data for a branch."""
    branchId: int = Field(description="Branch ID")
    onHand: float = Field(description="Available stock quantity")
    reserved: float = Field(description="Reserved stock quantity")


class ProductOnHand(BaseModel):
    """Individual product with inventory data."""
    id: int = Field(description="Product ID")
    code: str = Field(description="Product code")
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None
    inventories: List[Inventory] = Field(description="Inventory data for each branch")


class ProductOnHandResponse(BaseModel):
    """Response model for product on hand API endpoint."""
    total: int = Field(description="Total number of products")
    pageSize: int = Field(description="Items per page")
    data: List[ProductOnHand] = Field(description="Product inventory data")


class Branch(BaseModel):
    """Individual branch data model."""
    id: int = Field(description="Branch ID")
    branchName: str = Field(description="Branch name")
    contactNumber: Optional[str] = None
    address: Optional[str] = None
    locationName: Optional[str] = None
    isActive: Optional[bool] = None
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None


class BranchResponse(BaseModel):
    """Response model for branches API endpoint."""
    total: int = Field(description="Total number of branches")
    pageSize: int = Field(description="Items per page")
    data: List[Branch] = Field(description="Branch data")


class Product(BaseModel):
    """Individual product data model."""
    id: int = Field(description="Product ID")
    code: str = Field(description="Product code")
    name: str = Field(description="Product name")
    fullName: Optional[str] = None
    categoryId: Optional[int] = None
    categoryName: Optional[str] = None
    basePrice: Optional[float] = None
    isActive: Optional[bool] = None
    allowsSale: Optional[bool] = None
    isService: Optional[bool] = None
    hasVariants: Optional[bool] = None
    description: Optional[str] = None
    weight: Optional[float] = None
    conversionValue: Optional[float] = None
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None


class Customer(BaseModel):
    """Mô hình dữ liệu khách hàng riêng lẻ."""
    id: int = Field(description="ID khách hàng")
    code: str = Field(description="Mã khách hàng")
    name: str = Field(description="Tên khách hàng")
    contactNumber: Optional[str] = None
    address: Optional[str] = None
    wardName: Optional[str] = None
    locationName: Optional[str] = None
    branchId: Optional[int] = None
    branchName: Optional[str] = None
    debt: Optional[float] = None
    totalInvoiced: Optional[float] = None
    totalPoint: Optional[float] = None
    email: Optional[str] = None
    birthDate: Optional[datetime] = None
    gender: Optional[bool] = None
    isActive: Optional[bool] = None
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None
    retailerId: Optional[int] = None
    groupId: Optional[int] = None
    groupName: Optional[str] = None


class CustomerResponse(BaseModel):
    """Mô hình phản hồi cho endpoint API khách hàng."""
    total: int = Field(description="Tổng số khách hàng")
    pageSize: int = Field(description="Số mục trên mỗi trang")
    data: List[Customer] = Field(description="Dữ liệu khách hàng")


class PurchaseOrderDetail(BaseModel):
    """Mô hình dữ liệu chi tiết đơn nhập hàng."""
    productId: int = Field(description="ID sản phẩm")
    ProductCode: str = Field(description="Mã sản phẩm")
    productName: str = Field(description="Tên sản phẩm")
    quantity: float = Field(description="Số lượng")
    price: float = Field(description="Giá")
    discount: Optional[str] = None
    serialNumbers: Optional[str] = None
    productBatchExpire: Optional[ProductBatchExpire] = None


class PurchaseOrder(BaseModel):
    """Mô hình dữ liệu đơn nhập hàng riêng lẻ."""
    id: int = Field(description="ID đơn nhập hàng")
    code: str = Field(description="Mã đơn nhập hàng")
    branchId: Optional[int] = None
    branchName: Optional[str] = None
    purchaseDate: Optional[datetime] = None
    discountRatio: Optional[float] = None
    total: Optional[float] = None
    supplierId: Optional[int] = None
    supplierName: Optional[str] = None
    supplierCode: Optional[str] = None
    partnerType: Optional[str] = None
    purchaseById: Optional[int] = None
    purchaseName: Optional[int] = None  # Theo API spec, đây là int
    purchaseOrderDetails: Optional[List[PurchaseOrderDetail]] = None


class PurchaseOrderResponse(BaseModel):
    """Mô hình phản hồi cho endpoint API đơn nhập hàng."""
    total: int = Field(description="Tổng số đơn nhập hàng")
    pageSize: int = Field(description="Số mục trên mỗi trang")
    data: List[PurchaseOrder] = Field(description="Dữ liệu đơn nhập hàng")


class Return(BaseModel):
    """Mô hình dữ liệu đơn trả hàng riêng lẻ."""
    id: int = Field(description="ID đơn trả hàng")
    code: str = Field(description="Mã đơn trả hàng")
    returnDate: Optional[datetime] = None
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None
    branchId: Optional[int] = None
    branchName: Optional[str] = None
    customerId: Optional[int] = None
    customerName: Optional[str] = None
    customerCode: Optional[str] = None
    invoiceId: Optional[int] = None
    invoiceCode: Optional[str] = None
    description: Optional[str] = None
    status: Optional[int] = None
    statusValue: Optional[str] = None
    total: Optional[float] = None
    discount: Optional[float] = None
    discountRatio: Optional[float] = None
    createdBy: Optional[str] = None
    modifiedBy: Optional[str] = None
    soldById: Optional[int] = None
    soldByName: Optional[str] = None


class ReturnResponse(BaseModel):
    """Mô hình phản hồi cho endpoint API đơn trả hàng."""
    total: int = Field(description="Tổng số đơn trả hàng")
    pageSize: int = Field(description="Số mục trên mỗi trang")
    data: List[Return] = Field(description="Dữ liệu đơn trả hàng")


class CashFlow(BaseModel):
    """Mô hình dữ liệu phiếu thu chi riêng lẻ."""
    id: int = Field(description="ID phiếu thu chi")
    code: str = Field(description="Mã phiếu thu chi")
    transactionDate: Optional[datetime] = None
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None
    branchId: Optional[int] = None
    branchName: Optional[str] = None
    type: Optional[int] = None
    typeValue: Optional[str] = None
    amount: Optional[float] = None
    description: Optional[str] = None
    reference: Optional[str] = None
    createdBy: Optional[int] = None
    modifiedBy: Optional[str] = None
    customerId: Optional[int] = None
    customerName: Optional[str] = None
    customerCode: Optional[str] = None
    supplierId: Optional[int] = None
    supplierName: Optional[str] = None
    invoiceId: Optional[int] = None
    invoiceCode: Optional[str] = None
    purchaseOrderId: Optional[int] = None
    purchaseOrderCode: Optional[str] = None


class CashFlowResponse(BaseModel):
    """Mô hình phản hồi cho endpoint API phiếu thu chi."""
    total: int = Field(description="Tổng số phiếu thu chi")
    pageSize: int = Field(description="Số mục trên mỗi trang")
    data: List[CashFlow] = Field(description="Dữ liệu phiếu thu chi")
