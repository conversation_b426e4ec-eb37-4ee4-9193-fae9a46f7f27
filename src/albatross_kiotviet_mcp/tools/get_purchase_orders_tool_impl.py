"""Triển khai công cụ đơn nhập hàng với các tùy chọn lọc toàn diện."""

from typing import Dict, Any, Optional, List
from loguru import logger
from .core.base_tool import BaseTool


class PurchaseOrdersToolImpl(BaseTool):
    """Triển khai công cụ đơn nhập hàng với khả năng lọc toàn diện."""
    
    async def execute(
        self,
        page_size: int = 20,
        current_item: int = 0,
        order_direction: str = "Asc",
        # Tham số chuẩn theo API spec
        branchIds: Optional[List[int]] = None,
        status: Optional[List[int]] = None,
        includePayment: Optional[bool] = None,
        includeOrderDelivery: Optional[bool] = None,
        fromPurchaseDate: Optional[str] = None,
        toPurchaseDate: Optional[str] = None,
        # Tham số bổ sung
        from_created_date: Optional[str] = None,
        to_created_date: Optional[str] = None,
        from_modified_date: Optional[str] = None,
        to_modified_date: Optional[str] = None,
        ids: Optional[List[int]] = None,
        supplier_ids: Optional[List[int]] = None,
        order_by: Optional[str] = None,
        include_purchase_order_details: Optional[bool] = None
    ) -> Dict[str, Any]:
        """Thực thi công cụ đơn nhập hàng với các tùy chọn lọc theo API spec chuẩn.

        Args:
            page_size: Số lượng mục trên mỗi trang (mặc định 20, tối đa 100)
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Hướng sắp xếp

            # Tham số chuẩn theo API spec
            branchIds: ID chi nhánh (danh sách)
            status: Tình trạng đặt hàng (danh sách)
            includePayment: Có lấy thông tin thanh toán
            includeOrderDelivery: Có lấy thông tin giao hàng
            fromPurchaseDate: Từ ngày nhập hàng
            toPurchaseDate: Đến ngày nhập hàng

            # Tham số bổ sung
            from_created_date: Ngày bắt đầu cho bộ lọc ngày tạo
            to_created_date: Ngày kết thúc cho bộ lọc ngày tạo
            from_modified_date: Ngày bắt đầu cho bộ lọc ngày sửa đổi
            to_modified_date: Ngày kết thúc cho bộ lọc ngày sửa đổi
            ids: Danh sách ID đơn nhập hàng cụ thể cần lấy
            supplier_ids: Danh sách ID nhà cung cấp để lọc theo
            order_by: Trường để sắp xếp theo
            include_purchase_order_details: Bao gồm chi tiết dòng đơn nhập hàng

        Returns:
            Dictionary chứa dữ liệu đơn nhập hàng từ API

        Raises:
            ValueError: Nếu tham số không hợp lệ
        """
        logger.info(f"Đang lấy đơn nhập hàng với bộ lọc - page_size: {page_size}, current_item: {current_item}")
        
        # Xác thực tham số bằng phương thức lớp cơ sở
        self.validate_pagination(page_size, current_item)
        self.validate_order_direction(order_direction)
        
        # Ghi log các bộ lọc được áp dụng
        filters_applied = []
        if fromPurchaseDate or toPurchaseDate:
            filters_applied.append(f"ngày_nhập_hàng: {fromPurchaseDate} đến {toPurchaseDate}")
        if from_created_date or to_created_date:
            filters_applied.append(f"ngày_tạo: {from_created_date} đến {to_created_date}")
        if from_modified_date or to_modified_date:
            filters_applied.append(f"ngày_sửa: {from_modified_date} đến {to_modified_date}")
        if ids:
            filters_applied.append(f"ids: {ids}")
        if branchIds:
            filters_applied.append(f"chi_nhánh_ids: {branchIds}")
        if supplier_ids:
            filters_applied.append(f"nhà_cung_cấp_ids: {supplier_ids}")
        if status is not None:
            filters_applied.append(f"trạng_thái: {status}")
        if order_by:
            filters_applied.append(f"sắp_xếp_theo: {order_by}")
        if include_purchase_order_details:
            filters_applied.append("bao_gồm_chi_tiết: true")
        if includePayment:
            filters_applied.append("bao_gồm_thanh_toán: true")
        if includeOrderDelivery:
            filters_applied.append("bao_gồm_giao_hàng: true")
        
        if filters_applied:
            logger.info(f"Bộ lọc được áp dụng: {', '.join(filters_applied)}")
        
        # Thực hiện gọi API
        result = await self.api_client.get_purchase_orders(
            page_size=page_size,
            current_item=current_item,
            order_direction=order_direction,
            # Tham số chuẩn theo API spec
            branchIds=branchIds,
            status=status,
            includePayment=includePayment,
            includeOrderDelivery=includeOrderDelivery,
            fromPurchaseDate=fromPurchaseDate,
            toPurchaseDate=toPurchaseDate,
            # Tham số bổ sung
            from_created_date=from_created_date,
            to_created_date=to_created_date,
            from_modified_date=from_modified_date,
            to_modified_date=to_modified_date,
            ids=ids,
            supplier_ids=supplier_ids,
            order_by=order_by,
            include_purchase_order_details=include_purchase_order_details
        )
        
        logger.info(f"Đã lấy thành công {len(result.data)} đơn nhập hàng trong tổng số {result.total}")
        
        # Chuyển đổi thành dictionary để đầu ra nhất quán
        return result.model_dump()