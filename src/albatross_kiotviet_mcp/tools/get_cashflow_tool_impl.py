"""Triển khai công cụ phiếu thu chi với các tùy chọn lọc toàn di<PERSON>."""

from typing import Dict, Any, Optional, List
from loguru import logger
from .core.base_tool import BaseTool


class CashFlowToolImpl(BaseTool):
    """Triển khai công cụ phiếu thu chi với khả năng lọc toàn di<PERSON>n."""
    
    async def execute(
        self,
        page_size: int = 20,
        current_item: int = 0,
        order_direction: str = "Asc",
        # Tham số chuẩn theo API spec
        branchIds: Optional[List[int]] = None,
        code: Optional[List[str]] = None,
        userId: Optional[int] = None,
        accountId: Optional[int] = None,
        partnerType: Optional[str] = None,
        method: Optional[List[str]] = None,
        cashFlowGroupId: Optional[List[int]] = None,
        usedForFinancialReporting: Optional[int] = None,
        partnerName: Optional[str] = None,
        contactNumber: Optional[str] = None,
        isReceipt: Optional[bool] = None,
        includeAccount: Optional[bool] = None,
        includeBranch: Optional[bool] = None,
        includeUser: Optional[bool] = None,
        startDate: Optional[str] = None,
        endDate: Optional[str] = None,
        status: Optional[int] = None,
        ids: Optional[List[int]] = None
    ) -> Dict[str, Any]:
        """Thực thi công cụ phiếu thu chi với các tùy chọn lọc theo API spec chuẩn.

        Args:
            page_size: Số lượng mục trên mỗi trang (mặc định 20, tối đa 100)
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Hướng sắp xếp
            branchIds: ID chi nhánh
            code: Danh sách mã code của phiếu
            userId: Id người tạo
            accountId: Tài khoản nhận
            partnerType: Loại người nộp/nhận
            method: Danh sách phương thức thanh toán
            cashFlowGroupId: Loại thu/chi
            usedForFinancialReporting: Lọc theo kết qua kinh doanh
            partnerName: Tên người nộp/nhận
            contactNumber: Số điện thoại người nộp/nhận
            isReceipt: Theo phiếu thu/chi
            includeAccount: Lấy thông tin tài khoản ngân hàng hay không
            includeBranch: Lấy tên chi nhánh hay không
            includeUser: Lấy tên người tạo hay không
            startDate: Thời gian bắt đầu
            endDate: Thời gian kết thúc
            status: Trạng thái phiếu
            ids: Id phiếu thu/chi

        Returns:
            Dictionary chứa dữ liệu phiếu thu chi từ API

        Raises:
            ValueError: Nếu tham số không hợp lệ
        """
        logger.info(f"Đang lấy phiếu thu chi với bộ lọc - page_size: {page_size}, current_item: {current_item}")
        
        # Xác thực tham số bằng phương thức lớp cơ sở
        self.validate_pagination(page_size, current_item)
        self.validate_order_direction(order_direction)
        
        # Ghi log các bộ lọc được áp dụng
        filters_applied = []
        if startDate or endDate:
            filters_applied.append(f"thời_gian: {startDate} đến {endDate}")
        if ids:
            filters_applied.append(f"ids: {ids}")
        if branchIds:
            filters_applied.append(f"chi_nhánh_ids: {branchIds}")
        if code:
            filters_applied.append(f"mã_phiếu: {code}")
        if userId is not None:
            filters_applied.append(f"người_tạo_id: {userId}")
        if accountId is not None:
            filters_applied.append(f"tài_khoản_id: {accountId}")
        if partnerType:
            partner_types = {"A": "tất cả", "C": "khách hàng", "S": "nhà cung cấp", "U": "nhân viên", "D": "đối tác giao hàng", "O": "khác"}
            filters_applied.append(f"loại_đối_tác: {partner_types.get(partnerType, partnerType)}")
        if method:
            filters_applied.append(f"phương_thức_thanh_toán: {method}")
        if cashFlowGroupId:
            filters_applied.append(f"nhóm_thu_chi_ids: {cashFlowGroupId}")
        if usedForFinancialReporting is not None:
            reporting_status = "đưa vào hoạch toán" if usedForFinancialReporting == 1 else "không hoạch toán"
            filters_applied.append(f"báo_cáo_tài_chính: {reporting_status}")
        if partnerName:
            filters_applied.append(f"tên_đối_tác: {partnerName}")
        if contactNumber:
            filters_applied.append(f"số_điện_thoại: {contactNumber}")
        if isReceipt is not None:
            receipt_type = "Thu" if isReceipt else "Chi"
            filters_applied.append(f"loại_phiếu: {receipt_type}")
        if status is not None:
            status_name = "Đã thanh toán" if status == 0 else "Đã hủy" if status == 1 else f"Trạng thái {status}"
            filters_applied.append(f"trạng_thái: {status_name}")

        # Log include options
        include_options = []
        if includeAccount:
            include_options.append("tài_khoản")
        if includeBranch:
            include_options.append("chi_nhánh")
        if includeUser:
            include_options.append("người_tạo")
        if include_options:
            filters_applied.append(f"bao_gồm: {', '.join(include_options)}")

        if filters_applied:
            logger.info(f"Bộ lọc được áp dụng: {', '.join(filters_applied)}")

        # Thực hiện gọi API
        result = await self.api_client.get_cashflow(
            page_size=page_size,
            current_item=current_item,
            order_direction=order_direction,
            # Tham số chuẩn theo API spec
            branchIds=branchIds,
            code=code,
            userId=userId,
            accountId=accountId,
            partnerType=partnerType,
            method=method,
            cashFlowGroupId=cashFlowGroupId,
            usedForFinancialReporting=usedForFinancialReporting,
            partnerName=partnerName,
            contactNumber=contactNumber,
            isReceipt=isReceipt,
            includeAccount=includeAccount,
            includeBranch=includeBranch,
            includeUser=includeUser,
            startDate=startDate,
            endDate=endDate,
            status=status,
            ids=ids
        )
        
        logger.info(f"Đã lấy thành công {len(result.data)} phiếu thu chi trong tổng số {result.total}")
        
        # Chuyển đổi thành dictionary để đầu ra nhất quán
        return result.model_dump()