"""Triển khai công cụ phiếu thu chi với các tùy chọn lọc toàn di<PERSON>n."""

from typing import Dict, Any, Optional, List
from loguru import logger
from .core.base_tool import BaseTool


class CashFlowToolImpl(BaseTool):
    """Triển khai công cụ phiếu thu chi với khả năng lọc toàn diện."""
    
    async def execute(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        branch_ids: Optional[List[int]] = None,
        codes: Optional[List[str]] = None,
        user_id: Optional[int] = None,
        account_id: Optional[int] = None,
        partner_type: Optional[str] = None,
        methods: Optional[List[str]] = None,
        cashflow_group_ids: Optional[List[int]] = None,
        used_for_financial_reporting: Optional[int] = None,
        partner_name: Optional[str] = None,
        contact_number: Optional[str] = None,
        is_receipt: Optional[bool] = None,
        include_account: Optional[bool] = None,
        include_branch: Optional[bool] = None,
        include_user: Optional[bool] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        status: Optional[int] = None,
        ids: Optional[List[int]] = None,
        order_by: Optional[str] = None
    ) -> Dict[str, Any]:
        """Thực thi công cụ phiếu thu chi với các tùy chọn lọc toàn diện theo API chính thức.
        
        Args:
            page_size: Số lượng mục trên mỗi trang
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Hướng sắp xếp
            branch_ids: Danh sách ID chi nhánh
            codes: Danh sách mã code của phiếu
            user_id: ID người tạo
            account_id: ID tài khoản nhận
            partner_type: Loại người nộp/nhận
            methods: Danh sách phương thức thanh toán
            cashflow_group_ids: Danh sách ID loại thu/chi
            used_for_financial_reporting: Lọc theo kết quả kinh doanh
            partner_name: Tên người nộp/nhận
            contact_number: Số điện thoại người nộp/nhận
            is_receipt: Phiếu thu/chi
            include_account: Lấy thông tin tài khoản ngân hàng
            include_branch: Lấy tên chi nhánh
            include_user: Lấy tên người tạo
            start_date: Thời gian bắt đầu
            end_date: Thời gian kết thúc
            status: Trạng thái phiếu
            ids: Danh sách ID phiếu thu chi cụ thể
            order_by: Trường để sắp xếp theo
            
        Returns:
            Dictionary chứa dữ liệu phiếu thu chi từ API
            
        Raises:
            ValueError: Nếu tham số không hợp lệ
        """
        logger.info(f"Đang lấy phiếu thu chi với bộ lọc - page_size: {page_size}, current_item: {current_item}")
        
        # Xác thực tham số bằng phương thức lớp cơ sở
        self.validate_pagination(page_size, current_item)
        self.validate_order_direction(order_direction)
        
        # Ghi log các bộ lọc được áp dụng
        filters_applied = []
        if start_date or end_date:
            filters_applied.append(f"thời_gian: {start_date} đến {end_date}")
        if ids:
            filters_applied.append(f"ids: {ids}")
        if branch_ids:
            filters_applied.append(f"chi_nhánh_ids: {branch_ids}")
        if codes:
            filters_applied.append(f"mã_phiếu: {codes}")
        if user_id is not None:
            filters_applied.append(f"người_tạo_id: {user_id}")
        if account_id is not None:
            filters_applied.append(f"tài_khoản_id: {account_id}")
        if partner_type:
            partner_types = {"A": "tất cả", "C": "khách hàng", "S": "nhà cung cấp", "U": "nhân viên", "D": "đối tác giao hàng", "O": "khác"}
            filters_applied.append(f"loại_đối_tác: {partner_types.get(partner_type, partner_type)}")
        if methods:
            filters_applied.append(f"phương_thức_thanh_toán: {methods}")
        if cashflow_group_ids:
            filters_applied.append(f"nhóm_thu_chi_ids: {cashflow_group_ids}")
        if used_for_financial_reporting is not None:
            reporting_status = "đưa vào hoạch toán" if used_for_financial_reporting == 1 else "không hoạch toán"
            filters_applied.append(f"báo_cáo_tài_chính: {reporting_status}")
        if partner_name:
            filters_applied.append(f"tên_đối_tác: {partner_name}")
        if contact_number:
            filters_applied.append(f"số_điện_thoại: {contact_number}")
        if is_receipt is not None:
            receipt_type = "Thu" if is_receipt else "Chi"
            filters_applied.append(f"loại_phiếu: {receipt_type}")
        if status is not None:
            status_name = "Đã thanh toán" if status == 0 else "Đã hủy" if status == 1 else f"Trạng thái {status}"
            filters_applied.append(f"trạng_thái: {status_name}")
        if order_by:
            filters_applied.append(f"sắp_xếp_theo: {order_by}")
        
        # Log include options
        include_options = []
        if include_account:
            include_options.append("tài_khoản")
        if include_branch:
            include_options.append("chi_nhánh")
        if include_user:
            include_options.append("người_tạo")
        if include_options:
            filters_applied.append(f"bao_gồm: {', '.join(include_options)}")
        
        if filters_applied:
            logger.info(f"Bộ lọc được áp dụng: {', '.join(filters_applied)}")
        
        # Thực hiện gọi API
        result = await self.api_client.get_cashflow(
            page_size=page_size,
            current_item=current_item,
            order_direction=order_direction,
            branch_ids=branch_ids,
            codes=codes,
            user_id=user_id,
            account_id=account_id,
            partner_type=partner_type,
            methods=methods,
            cashflow_group_ids=cashflow_group_ids,
            used_for_financial_reporting=used_for_financial_reporting,
            partner_name=partner_name,
            contact_number=contact_number,
            is_receipt=is_receipt,
            include_account=include_account,
            include_branch=include_branch,
            include_user=include_user,
            start_date=start_date,
            end_date=end_date,
            status=status,
            ids=ids,
            order_by=order_by
        )
        
        logger.info(f"Đã lấy thành công {len(result.data)} phiếu thu chi trong tổng số {result.total}")
        
        # Chuyển đổi thành dictionary để đầu ra nhất quán
        return result.model_dump()