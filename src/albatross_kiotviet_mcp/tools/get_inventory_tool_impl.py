"""Inventory tool implementation using new architecture."""

from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from .core.base_tool import BaseTool


class InventoryToolImpl(BaseTool):
    """Implementation for inventory tool."""
    
    async def execute(
        self,
        page_size: int = 20,
        current_item: int = 0,
        order_by: Optional[str] = None,
        last_modified_from: Optional[Union[str, datetime]] = None,
        branch_ids: Optional[List[int]] = None
    ) -> Dict[str, Any]:
        """Execute inventory tool logic.
        
        Args:
            page_size: Number of items per page
            current_item: Starting item index for pagination
            order_by: Sort data by field (e.g., "Code")
            last_modified_from: Filter by last modified time (ISO datetime string)
            branch_ids: List of branch IDs to filter by
            
        Returns:
            Dictionary containing inventory data with product details and stock levels
            
        Raises:
            ValueError: If parameters are invalid
        """
        # Validate pagination parameters
        self.validate_pagination(page_size, current_item)
        
        # Make API call
        result = await self.api_client.get_product_on_hands(
            page_size=page_size,
            current_item=current_item,
            order_by=order_by,
            last_modified_from=last_modified_from,
            branch_ids=branch_ids
        )
        
        # Convert to dictionary for consistent output
        return result.model_dump()