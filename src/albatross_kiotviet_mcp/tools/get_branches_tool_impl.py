"""Branches tool implementation using new architecture."""

from typing import Dict, Any
from .core.base_tool import BaseTool


class BranchesToolImpl(BaseTool):
    """Implementation for branches tool."""
    
    async def execute(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc"
    ) -> Dict[str, Any]:
        """Execute branches tool logic.
        
        Args:
            page_size: Number of items per page
            current_item: Starting item index for pagination
            order_direction: Sort order direction
            
        Returns:
            Dictionary containing branch data from the API
            
        Raises:
            ValueError: If parameters are invalid
        """
        # Validate parameters using base class methods
        self.validate_pagination(page_size, current_item)
        self.validate_order_direction(order_direction)
        
        # Make API call
        result = await self.api_client.get_branches(
            page_size=page_size,
            current_item=current_item,
            order_direction=order_direction
        )
        
        # Convert to dictionary for consistent output
        return result.model_dump()