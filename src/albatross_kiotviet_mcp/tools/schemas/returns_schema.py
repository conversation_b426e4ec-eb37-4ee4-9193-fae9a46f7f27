"""Schema cho tham số công cụ đơn trả hàng."""

from typing import Optional, List
from pydantic import BaseModel, Field
from .common_schemas import PaginationParams


class ReturnsParams(PaginationParams):
    """Tham số cho công cụ đơn trả hàng theo API spec chuẩn."""

    # Tham số chuẩn theo API spec
    orderBy: Optional[str] = Field(
        default=None,
        description="Sắp xếp dữ liệu theo trường orderBy (ví dụ: orderBy=Name)"
    )

    lastModifiedFrom: Optional[str] = Field(
        default=None,
        description="Th<PERSON><PERSON> gian cập nhật (định dạng datetime)"
    )

    fromReturnDate: Optional[str] = Field(
        default=None,
        description="Từ ngày trả hàng (định dạng YYYY-MM-DD)"
    )

    toReturnDate: Optional[str] = Field(
        default=None,
        description="Đến ngày trả hàng (định dạng YYYY-MM-DD)"
    )

    includePayment: Optional[bool] = Field(
        default=False,
        description="C<PERSON> lấy thông tin danh sách thanh toán"
    )

    # Các tham số bổ sung (không có trong API spec chuẩn nhưng hữu ích)
    from_created_date: Optional[str] = Field(
        default=None,
        description="Ngày bắt đầu cho bộ lọc ngày tạo (định dạng YYYY-MM-DD)"
    )
    to_created_date: Optional[str] = Field(
        default=None,
        description="Ngày kết thúc cho bộ lọc ngày tạo (định dạng YYYY-MM-DD)"
    )
    from_modified_date: Optional[str] = Field(
        default=None,
        description="Ngày bắt đầu cho bộ lọc ngày sửa đổi (định dạng YYYY-MM-DD)"
    )
    to_modified_date: Optional[str] = Field(
        default=None,
        description="Ngày kết thúc cho bộ lọc ngày sửa đổi (định dạng YYYY-MM-DD)"
    )

    # Lọc ID
    ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID đơn trả hàng cụ thể cần lấy"
    )

    # Lọc chi nhánh
    branch_ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID chi nhánh để lọc theo"
    )

    # Lọc khách hàng
    customer_ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID khách hàng để lọc theo"
    )

    # Lọc hóa đơn gốc
    invoice_ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID hóa đơn gốc để lọc theo"
    )

    # Lọc trạng thái
    status: Optional[int] = Field(
        default=None,
        description="Lọc theo trạng thái đơn trả hàng (1=Chờ duyệt, 2=Đã duyệt, 3=Đã hủy, v.v.)"
    )

    # Tùy chọn bao gồm chi tiết
    include_return_details: Optional[bool] = Field(
        default=False,
        description="Bao gồm chi tiết dòng đơn trả hàng"
    )