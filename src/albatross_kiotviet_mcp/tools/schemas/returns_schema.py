"""Schema cho tham số công cụ đơn trả hàng."""

from typing import Optional, List
from pydantic import BaseModel, Field
from .common_schemas import PaginationParams


class ReturnsParams(PaginationParams):
    """Tham số cho công cụ đơn trả hàng với các tùy chọn lọc toàn diện."""
    
    # Lọc ngày
    from_return_date: Optional[str] = Field(
        default=None,
        description="Ngày bắt đầu cho bộ lọc ngày trả hàng (định dạng YYYY-MM-DD)"
    )
    to_return_date: Optional[str] = Field(
        default=None,
        description="Ng<PERSON>y kết thúc cho bộ lọc ngày trả hàng (định dạng YYYY-MM-DD)"
    )
    from_created_date: Optional[str] = Field(
        default=None,
        description="Ngày bắt đầu cho bộ lọc ngày tạo (định dạng YYYY-MM-DD)"
    )
    to_created_date: Optional[str] = Field(
        default=None,
        description="<PERSON><PERSON><PERSON> kết thúc cho bộ lọc ngày tạo (định dạng YYYY-MM-DD)"
    )
    from_modified_date: Optional[str] = Field(
        default=None,
        description="Ngày bắt đầu cho bộ lọc ngày sửa đổi (định dạng YYYY-MM-DD)"
    )
    to_modified_date: Optional[str] = Field(
        default=None,
        description="Ngày kết thúc cho bộ lọc ngày sửa đổi (định dạng YYYY-MM-DD)"
    )
    
    # Lọc ID
    ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID đơn trả hàng cụ thể cần lấy"
    )
    
    # Lọc chi nhánh
    branch_ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID chi nhánh để lọc theo"
    )
    
    # Lọc khách hàng
    customer_ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID khách hàng để lọc theo"
    )
    
    # Lọc hóa đơn gốc
    invoice_ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID hóa đơn gốc để lọc theo"
    )
    
    # Lọc trạng thái
    status: Optional[int] = Field(
        default=None,
        description="Lọc theo trạng thái đơn trả hàng (1=Chờ duyệt, 2=Đã duyệt, 3=Đã hủy, v.v.)"
    )
    
    # Tùy chọn sắp xếp
    order_by: Optional[str] = Field(
        default=None,
        description="Trường để sắp xếp theo (ví dụ: 'returnDate', 'total')"
    )
    
    # Tùy chọn bao gồm chi tiết
    include_return_details: Optional[bool] = Field(
        default=False,
        description="Bao gồm chi tiết dòng đơn trả hàng"
    )