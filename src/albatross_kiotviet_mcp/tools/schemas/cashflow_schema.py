"""Schema cho tham số công cụ phiếu thu chi."""

from typing import Optional, List
from pydantic import BaseModel, Field
from .common_schemas import PaginationParams


class CashFlowParams(PaginationParams):
    """Tham số cho công cụ phiếu thu chi với các tùy chọn lọc toàn diện theo API KiotViet."""
    
    # Lọc theo ID chi nhánh
    branch_ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID chi nhánh để lọc theo"
    )
    
    # Lọc theo mã code phiếu
    codes: Optional[List[str]] = Field(
        default=None,
        description="Danh sách mã code của phiếu thu chi"
    )
    
    # Lọc theo người tạo
    user_id: Optional[int] = Field(
        default=None,
        description="ID người tạo phiếu"
    )
    
    # Lọc theo tài khoản nhận
    account_id: Optional[int] = Field(
        default=None,
        description="ID tài khoản nhận"
    )
    
    # Lọc theo loại người nộp/nhận
    partner_type: Optional[str] = Field(
        default=None,
        description="Loại người nộp/nhận: A=tất cả, C=khách hàng, S=nhà cung cấp, U=nhân viên, D=đối tác giao hàng, O=khác"
    )
    
    # Lọc theo phương thức thanh toán
    methods: Optional[List[str]] = Field(
        default=None,
        description="Danh sách phương thức thanh toán"
    )
    
    # Lọc theo loại thu/chi
    cashflow_group_ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID loại thu/chi"
    )
    
    # Lọc theo kết quả kinh doanh
    used_for_financial_reporting: Optional[int] = Field(
        default=None,
        description="Lọc theo kết quả kinh doanh: 0=không hoạch toán, 1=đưa vào hoạch toán"
    )
    
    # Lọc theo tên người nộp/nhận
    partner_name: Optional[str] = Field(
        default=None,
        description="Tên người nộp/nhận"
    )
    
    # Lọc theo số điện thoại
    contact_number: Optional[str] = Field(
        default=None,
        description="Số điện thoại người nộp/nhận"
    )
    
    # Lọc theo phiếu thu/chi
    is_receipt: Optional[bool] = Field(
        default=None,
        description="Lọc theo phiếu thu/chi: True=thu, False=chi"
    )
    
    # Tùy chọn bao gồm thông tin
    include_account: Optional[bool] = Field(
        default=False,
        description="Lấy thông tin tài khoản ngân hàng hay không"
    )
    
    include_branch: Optional[bool] = Field(
        default=False,
        description="Lấy tên chi nhánh hay không"
    )
    
    include_user: Optional[bool] = Field(
        default=False,
        description="Lấy tên người tạo hay không"
    )
    
    # Lọc theo thời gian
    start_date: Optional[str] = Field(
        default=None,
        description="Thời gian bắt đầu (định dạng YYYY-MM-DD)"
    )
    
    end_date: Optional[str] = Field(
        default=None,
        description="Thời gian kết thúc (định dạng YYYY-MM-DD)"
    )
    
    # Lọc theo trạng thái
    status: Optional[int] = Field(
        default=None,
        description="Trạng thái phiếu: 0=Đã thanh toán, 1=Đã hủy, không truyền=tất cả"
    )
    
    # Lọc theo ID phiếu
    ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID phiếu thu chi cụ thể cần lấy"
    )