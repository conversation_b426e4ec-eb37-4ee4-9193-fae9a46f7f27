"""Schema cho tham số công cụ đơn nhập hàng."""

from typing import Optional, List
from pydantic import BaseModel, Field
from .common_schemas import PaginationParams


class PurchaseOrdersParams(PaginationParams):
    """Tham số cho công cụ đơn nhập hàng với các tùy chọn lọc toàn diện."""
    
    # Lọc ngày
    from_purchase_date: Optional[str] = Field(
        default=None,
        description="Ngày bắt đầu cho bộ lọc ngày nhập hàng (định dạng YYYY-MM-DD)"
    )
    to_purchase_date: Optional[str] = Field(
        default=None,
        description="<PERSON><PERSON>y kết thúc cho bộ lọc ngày nhập hàng (định dạng YYYY-MM-DD)"
    )
    from_created_date: Optional[str] = Field(
        default=None,
        description="<PERSON><PERSON>y bắt đầu cho bộ lọc ngày tạo (định dạng YYYY-MM-DD)"
    )
    to_created_date: Optional[str] = Field(
        default=None,
        description="<PERSON><PERSON><PERSON> kết thúc cho bộ lọc ngày tạo (định dạng YYYY-MM-DD)"
    )
    from_modified_date: Optional[str] = Field(
        default=None,
        description="Ngày bắt đầu cho bộ lọc ngày sửa đổi (định dạng YYYY-MM-DD)"
    )
    to_modified_date: Optional[str] = Field(
        default=None,
        description="Ngày kết thúc cho bộ lọc ngày sửa đổi (định dạng YYYY-MM-DD)"
    )
    
    # Lọc ID
    ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID đơn nhập hàng cụ thể cần lấy"
    )
    
    # Lọc chi nhánh
    branch_ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID chi nhánh để lọc theo"
    )
    
    # Lọc nhà cung cấp
    supplier_ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID nhà cung cấp để lọc theo"
    )
    
    # Lọc trạng thái
    status: Optional[int] = Field(
        default=None,
        description="Lọc theo trạng thái đơn nhập hàng (1=Chờ duyệt, 2=Đã duyệt, 3=Đã hủy, v.v.)"
    )
    
    # Tùy chọn sắp xếp
    order_by: Optional[str] = Field(
        default=None,
        description="Trường để sắp xếp theo (ví dụ: 'purchaseDate', 'total')"
    )
    
    # Tùy chọn bao gồm chi tiết
    include_purchase_order_details: Optional[bool] = Field(
        default=False,
        description="Bao gồm chi tiết dòng đơn nhập hàng"
    )