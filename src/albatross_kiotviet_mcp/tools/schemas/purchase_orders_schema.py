"""Schema cho tham số công cụ đơn nhập hàng."""

from typing import Optional, List
from pydantic import BaseModel, Field
from .common_schemas import PaginationParams


class PurchaseOrdersParams(PaginationParams):
    """Tham số cho công cụ đơn nhập hàng theo API spec chuẩn."""

    # Tham số chuẩn theo API spec
    branchIds: Optional[List[int]] = Field(
        default=None,
        description="ID chi nhánh (danh sách)"
    )

    status: Optional[List[int]] = Field(
        default=None,
        description="Tình trạng đặt hàng (danh sách)"
    )

    includePayment: Optional[bool] = Field(
        default=False,
        description="Có lấy thông tin thanh toán"
    )

    includeOrderDelivery: Optional[bool] = Field(
        default=False,
        description="Có lấy thông tin giao hàng"
    )

    fromPurchaseDate: Optional[str] = Field(
        default=None,
        description="Từ ngày nhập hàng (định dạng YYYY-MM-DD)"
    )

    toPurchaseDate: Optional[str] = Field(
        default=None,
        description="Đến ngày nhập hàng (định dạng YYYY-MM-DD)"
    )

    # Các tham số bổ sung (không có trong API spec chuẩn nhưng hữu ích)
    from_created_date: Optional[str] = Field(
        default=None,
        description="Ngày bắt đầu cho bộ lọc ngày tạo (định dạng YYYY-MM-DD)"
    )
    to_created_date: Optional[str] = Field(
        default=None,
        description="Ngày kết thúc cho bộ lọc ngày tạo (định dạng YYYY-MM-DD)"
    )
    from_modified_date: Optional[str] = Field(
        default=None,
        description="Ngày bắt đầu cho bộ lọc ngày sửa đổi (định dạng YYYY-MM-DD)"
    )
    to_modified_date: Optional[str] = Field(
        default=None,
        description="Ngày kết thúc cho bộ lọc ngày sửa đổi (định dạng YYYY-MM-DD)"
    )

    # Lọc ID
    ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID đơn nhập hàng cụ thể cần lấy"
    )

    # Lọc nhà cung cấp
    supplier_ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID nhà cung cấp để lọc theo"
    )

    # Tùy chọn sắp xếp
    order_by: Optional[str] = Field(
        default=None,
        description="Trường để sắp xếp theo (ví dụ: 'purchaseDate', 'total')"
    )

    # Tùy chọn bao gồm chi tiết
    include_purchase_order_details: Optional[bool] = Field(
        default=False,
        description="Bao gồm chi tiết dòng đơn nhập hàng"
    )