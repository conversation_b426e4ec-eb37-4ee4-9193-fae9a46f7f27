"""Triển khai công cụ đơn trả hàng với các tùy chọn lọc toàn diện."""

from typing import Dict, Any, Optional, List
from loguru import logger
from .core.base_tool import BaseTool


class ReturnsToolImpl(BaseTool):
    """Triển khai công cụ đơn trả hàng với khả năng lọc toàn diện."""
    
    async def execute(
        self,
        page_size: int = 20,
        current_item: int = 0,
        order_direction: str = "Asc",
        # Tham số chuẩn theo API spec
        orderBy: Optional[str] = None,
        lastModifiedFrom: Optional[str] = None,
        fromReturnDate: Optional[str] = None,
        toReturnDate: Optional[str] = None,
        includePayment: Optional[bool] = None
    ) -> Dict[str, Any]:
        """Thực thi công cụ đơn trả hàng với các tùy chọn lọc theo API spec chuẩn.

        Args:
            page_size: <PERSON><PERSON> lượng mục trên mỗi trang (mặc định 20, tối đa 100)
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Hướng sắp xếp
            orderBy: Sắp xếp dữ liệu theo trường orderBy (ví dụ: orderBy=Name)
            lastModifiedFrom: Thời gian cập nhật
            fromReturnDate: Từ ngày trả hàng
            toReturnDate: Đến ngày trả hàng
            includePayment: Có lấy thông tin danh sách thanh toán

        Returns:
            Dictionary chứa dữ liệu đơn trả hàng từ API

        Raises:
            ValueError: Nếu tham số không hợp lệ
        """
        logger.info(f"Đang lấy đơn trả hàng với bộ lọc - page_size: {page_size}, current_item: {current_item}")
        
        # Xác thực tham số bằng phương thức lớp cơ sở
        self.validate_pagination(page_size, current_item)
        self.validate_order_direction(order_direction)
        
        # Ghi log các bộ lọc được áp dụng
        filters_applied = []
        if fromReturnDate or toReturnDate:
            filters_applied.append(f"ngày_trả_hàng: {fromReturnDate} đến {toReturnDate}")
        if lastModifiedFrom:
            filters_applied.append(f"thời_gian_cập_nhật: {lastModifiedFrom}")
        if orderBy:
            filters_applied.append(f"sắp_xếp_theo: {orderBy}")
        if includePayment:
            filters_applied.append("bao_gồm_thanh_toán: true")
        
        if filters_applied:
            logger.info(f"Bộ lọc được áp dụng: {', '.join(filters_applied)}")
        
        # Thực hiện gọi API
        result = await self.api_client.get_returns(
            page_size=page_size,
            current_item=current_item,
            order_direction=order_direction,
            # Tham số chuẩn theo API spec
            orderBy=orderBy,
            lastModifiedFrom=lastModifiedFrom,
            fromReturnDate=fromReturnDate,
            toReturnDate=toReturnDate,
            includePayment=includePayment
        )
        
        logger.info(f"Đã lấy thành công {len(result.data)} đơn trả hàng trong tổng số {result.total}")
        
        # Chuyển đổi thành dictionary để đầu ra nhất quán
        return result.model_dump()