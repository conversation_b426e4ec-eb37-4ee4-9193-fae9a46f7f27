"""Triển khai công cụ đơn trả hàng với các tùy chọn lọc toàn di<PERSON>n."""

from typing import Dict, Any, Optional, List
from loguru import logger
from .core.base_tool import BaseTool


class ReturnsToolImpl(BaseTool):
    """Triển khai công cụ đơn trả hàng với khả năng lọc toàn diện."""
    
    async def execute(
        self,
        page_size: int = 20,
        current_item: int = 0,
        order_direction: str = "Asc",
        # Tham số chuẩn theo API spec
        orderBy: Optional[str] = None,
        lastModifiedFrom: Optional[str] = None,
        fromReturnDate: Optional[str] = None,
        toReturnDate: Optional[str] = None,
        includePayment: Optional[bool] = None,
        # Tham số bổ sung
        from_created_date: Optional[str] = None,
        to_created_date: Optional[str] = None,
        from_modified_date: Optional[str] = None,
        to_modified_date: Optional[str] = None,
        ids: Optional[List[int]] = None,
        branch_ids: Optional[List[int]] = None,
        customer_ids: Optional[List[int]] = None,
        invoice_ids: Optional[List[int]] = None,
        status: Optional[int] = None,
        include_return_details: Optional[bool] = None
    ) -> Dict[str, Any]:
        """Thực thi công cụ đơn trả hàng với các tùy chọn lọc theo API spec chuẩn.

        Args:
            page_size: Số lượng mục trên mỗi trang (mặc định 20, tối đa 100)
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Hướng sắp xếp

            # Tham số chuẩn theo API spec
            orderBy: Sắp xếp dữ liệu theo trường orderBy (ví dụ: orderBy=Name)
            lastModifiedFrom: Thời gian cập nhật
            fromReturnDate: Từ ngày trả hàng
            toReturnDate: Đến ngày trả hàng
            includePayment: Có lấy thông tin danh sách thanh toán

            # Tham số bổ sung
            from_created_date: Ngày bắt đầu cho bộ lọc ngày tạo
            to_created_date: Ngày kết thúc cho bộ lọc ngày tạo
            from_modified_date: Ngày bắt đầu cho bộ lọc ngày sửa đổi
            to_modified_date: Ngày kết thúc cho bộ lọc ngày sửa đổi
            ids: Danh sách ID đơn trả hàng cụ thể cần lấy
            branch_ids: Danh sách ID chi nhánh để lọc theo
            customer_ids: Danh sách ID khách hàng để lọc theo
            invoice_ids: Danh sách ID hóa đơn gốc để lọc theo
            status: Lọc theo trạng thái đơn trả hàng
            include_return_details: Bao gồm chi tiết dòng đơn trả hàng

        Returns:
            Dictionary chứa dữ liệu đơn trả hàng từ API

        Raises:
            ValueError: Nếu tham số không hợp lệ
        """
        logger.info(f"Đang lấy đơn trả hàng với bộ lọc - page_size: {page_size}, current_item: {current_item}")
        
        # Xác thực tham số bằng phương thức lớp cơ sở
        self.validate_pagination(page_size, current_item)
        self.validate_order_direction(order_direction)
        
        # Ghi log các bộ lọc được áp dụng
        filters_applied = []
        if fromReturnDate or toReturnDate:
            filters_applied.append(f"ngày_trả_hàng: {fromReturnDate} đến {toReturnDate}")
        if lastModifiedFrom:
            filters_applied.append(f"thời_gian_cập_nhật: {lastModifiedFrom}")
        if orderBy:
            filters_applied.append(f"sắp_xếp_theo: {orderBy}")
        if includePayment:
            filters_applied.append("bao_gồm_thanh_toán: true")
        if from_created_date or to_created_date:
            filters_applied.append(f"ngày_tạo: {from_created_date} đến {to_created_date}")
        if from_modified_date or to_modified_date:
            filters_applied.append(f"ngày_sửa: {from_modified_date} đến {to_modified_date}")
        if ids:
            filters_applied.append(f"ids: {ids}")
        if branch_ids:
            filters_applied.append(f"chi_nhánh_ids: {branch_ids}")
        if customer_ids:
            filters_applied.append(f"khách_hàng_ids: {customer_ids}")
        if invoice_ids:
            filters_applied.append(f"hóa_đơn_ids: {invoice_ids}")
        if status is not None:
            filters_applied.append(f"trạng_thái: {status}")
        if include_return_details:
            filters_applied.append("bao_gồm_chi_tiết: true")
        
        if filters_applied:
            logger.info(f"Bộ lọc được áp dụng: {', '.join(filters_applied)}")
        
        # Thực hiện gọi API
        result = await self.api_client.get_returns(
            page_size=page_size,
            current_item=current_item,
            order_direction=order_direction,
            # Tham số chuẩn theo API spec
            orderBy=orderBy,
            lastModifiedFrom=lastModifiedFrom,
            fromReturnDate=fromReturnDate,
            toReturnDate=toReturnDate,
            includePayment=includePayment,
            # Tham số bổ sung
            from_created_date=from_created_date,
            to_created_date=to_created_date,
            from_modified_date=from_modified_date,
            to_modified_date=to_modified_date,
            ids=ids,
            branch_ids=branch_ids,
            customer_ids=customer_ids,
            invoice_ids=invoice_ids,
            status=status,
            include_return_details=include_return_details
        )
        
        logger.info(f"Đã lấy thành công {len(result.data)} đơn trả hàng trong tổng số {result.total}")
        
        # Chuyển đổi thành dictionary để đầu ra nhất quán
        return result.model_dump()